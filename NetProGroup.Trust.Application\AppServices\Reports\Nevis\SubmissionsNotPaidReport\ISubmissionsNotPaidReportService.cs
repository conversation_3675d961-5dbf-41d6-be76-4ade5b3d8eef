// <copyright file="ISubmissionsNotPaidReportService.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

using NetProGroup.Framework.DependencyInjection.Interfaces;

namespace NetProGroup.Trust.Application.AppServices.Reports.Nevis.SubmissionsNotPaidReport
{
    /// <summary>
    /// Interface for submissions not paid report service.
    /// </summary>
    public interface ISubmissionsNotPaidReportService : ITransientService, IReportService;
}