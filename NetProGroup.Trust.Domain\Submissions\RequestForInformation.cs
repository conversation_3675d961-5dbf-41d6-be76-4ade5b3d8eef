// <copyright file="RequestForInformation.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

using NetProGroup.Framework.EF.Repository;
using NetProGroup.Framework.Services.Identity.EFModels;
using NetProGroup.Trust.DomainShared.Enums;
using System.ComponentModel.DataAnnotations;

namespace NetProGroup.Trust.Domain.Submissions
{
    /// <summary>
    /// Represents a Request for information event created for a submission.
    /// </summary>
    public class RequestForInformation : StampedEntity<Guid>
    {
        /// <summary>
        /// Gets or sets the id of the submission.
        /// </summary>
        public Guid SubmissionId { get; set; }

        /// <summary>
        /// Gets or sets the submission.
        /// </summary>
        public virtual Submission Submission { get; set; }

        /// <summary>
        /// Gets or sets the deadline date for the request for information.
        /// </summary>
        public DateTime DeadLine { get; set; }

        /// <summary>
        /// Gets or sets the comments for the request for information.
        /// </summary>
        public string Comments { get; set; }

        /// <summary>
        /// Gets or sets the client response for the request for information.
        /// </summary>
        public string Response { get; set; }

        /// <summary>
        /// Gets or sets the comments for the completed action for the request for information.
        /// </summary>
        public string Remark { get; set; }

        /// <summary>
        /// Gets or sets the overall status of the request for information.
        /// </summary>
        public RequestForInformationStatus Status { get; set; }

        /// <summary>
        /// Gets or sets the date of the last reminder sent to the user.
        /// </summary>
        public DateTime? LastRemindedAt { get; set; }

        /// <summary>
        /// Gets or sets the reminder type of the request for information.
        /// </summary>
        public RequestForInformationReminderType? ReminderType { get; set; }

        /// <summary>
        /// Gets or sets the date of the last reminder sent to the user.
        /// </summary>
        public DateTime? CompletedAt { get; set; }

        /// <summary>
        ///  Gets or sets the date/time that the request for information was replied to.
        /// </summary>
        public DateTime? RepliedAt { get; set; }

        /// <summary>
        /// Gets or sets the id of the user that replied to the request for information.
        /// </summary>
        public Guid? RepliedBy { get; set; }

        /// <summary>
        /// Gets or sets the user that completed the request for information.
        /// </summary>
        public Guid? CompletedBy { get; set; }

        /// <summary>
        /// Gets or sets the id of the user that created the RFI.
        /// </summary>
        public Guid CreatedBy { get; set; }

        /// <summary>
        /// Gets or sets the user that created the RFI.
        /// </summary>
        /// <value>The <see cref="ApplicationUser"/>.</value>
        public virtual ApplicationUser CreatedByUser { get; set; }

        /// <summary>
        /// Gets or sets the reason for cancellation.
        /// </summary>
        public string CancellationReason { get; set; }

        /// <summary>
        /// Gets or sets the user that replied to the request for information.
        /// </summary>
        /// <value>The <see cref="ApplicationUser"/>.</value>
        public virtual ApplicationUser RepliedByUser { get; set; }

        /// <summary>
        /// Gets or sets the user that completed the request for information.
        /// </summary>
        /// <value>The <see cref="ApplicationUser"/>.</value>
        public virtual ApplicationUser CompletedByUser { get; set; }

        /// <summary>
        /// Gets the collection with RequestForInformationDocument.
        /// </summary>
        /// <value>A collection of <see cref="RequestForInformationDocument"/>.</value>
        public virtual ICollection<RequestForInformationDocument> Documents { get; } = new List<RequestForInformationDocument>();
    }
}
