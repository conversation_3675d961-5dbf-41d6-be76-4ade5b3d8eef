﻿// <copyright file="ILegalEntitiesDataManager.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

using NetProGroup.Framework.DependencyInjection.Interfaces;
using NetProGroup.Trust.Application.Contracts.LegalEntities.Companies;
using NetProGroup.Trust.Application.Contracts.LegalEntities.Models;
using NetProGroup.Trust.DataManager.LegalEntities.RequestResponses;
using NetProGroup.Trust.DataManager.LegalEntities.Models;
using NetProGroup.Framework.Services.Locks.Models;
using Microsoft.EntityFrameworkCore;
using NetProGroup.Trust.Domain.LegalEntities;
using NetProGroup.Trust.Domain.Submissions;

namespace NetProGroup.Trust.DataManager.LegalEntities
{
    /// <summary>
    /// Interface for the LegalEntities DataManager.
    /// </summary>
    public interface ILegalEntitiesDataManager : IScopedService
    {
        /// <summary>
        /// Creates the company from the model.
        /// </summary>
        /// <param name="model">The inbound model.</param>
        /// <param name="saveChanges">Whether to save the changes immediately.</param>
        /// <returns>A <see cref="Task"/> representing the asynchronous operation.</returns>
        Task<CompanyDTO> CreateCompanyAsync(CreateCompanyDTO model, bool saveChanges = false);

        /// <summary>
        /// Syncs the LegalEntities.
        /// </summary>
        /// <param name="request">The request with all parameters.</param>
        /// <param name="jobLock">The optional lock for the sync job.</param>
        /// <param name="beforeCommitAsync">(Optional) function to execute before the commit.</param>
        /// <returns>A <see cref="Task"/> representing the asynchronous operation.</returns>
        Task SyncLegalEntitiesAsync(SyncLegalEntitiesRequest request, LockDTO jobLock = null, Func<DbContext, Task> beforeCommitAsync = null);

        /// <summary>
        /// Gets a paged list with companies.
        /// </summary>
        /// <param name="request">Request with optional parameters to search for.</param>
        /// <returns>A <see cref="Task{ListCompaniesResponse}"/> representing the result of the asynchronous operation.</returns>
        Task<ListCompaniesResponse> ListCompaniesAsync(ListCompaniesRequest request);

        /// <summary>
        /// Gets a list with companies.
        /// </summary>
        /// <param name="request">Request with parameters to search for.</param>
        /// <returns>A <see cref="Task{SearchCompaniesResponse}"/> representing the result of the asynchronous operation.</returns>
        Task<SearchCompaniesResponse> SearchCompaniesAsync(SearchCompaniesRequest request);

        /// <summary>
        /// Searches for companies with their annual fee status based on the provided request.
        /// </summary>
        /// <param name="request">The request containing the search parameters.</param>
        /// <returns>A <see cref="Task{SearchCompaniesWithAnnualFeeStatusResponse}"/> representing the asynchronous operation. The task result contains the response with the search results including annual fee status.</returns>
        Task<SearchCompaniesWithAnnualFeeStatusResponse> SearchCompaniesWithAnnualFeeStatusAsync(
            SearchCompanyWithAnnualFeeStatusRequest request);

        /// <summary>
        /// Updates the annual fee status for multiple companies in a single operation.
        /// </summary>
        /// <param name="companyIds">The list of company IDs to update.</param>
        /// <param name="financialYear">The financial year for which to update the status.</param>
        /// <param name="isPaid">Whether the annual fee is paid or not.</param>
        /// <param name="authorizedJurisdictionIDs">The jurisdiction ID's to update.</param>
        /// <returns>A task that represents the asynchronous operation.</returns>
        Task UpdateCompaniesAnnualFeeStatusAsync(List<Guid> companyIds, int financialYear, bool isPaid, List<Guid> authorizedJurisdictionIDs);

        /// <summary>
        /// Retrieves a company by its ID.
        /// </summary>
        /// <param name="companyId">The ID of the company to retrieve.</param>
        /// <returns>A <see cref="Task{CompanyDTO}"/> representing the asynchronous operation.</returns>
        Task<CompanyDTO> GetCompanyByIdAsync(Guid companyId);

        /// <summary>
        /// Approves a company's registration.
        /// </summary>
        /// <param name="companyId">The ID of the company to approve.</param>
        /// <returns>A task that represents the asynchronous operation.</returns>
        Task ApproveCompanyAsync(Guid companyId);

        /// <summary>
        /// Declines a company's registration.
        /// </summary>
        /// <param name="companyId">The ID of the company to decline.</param>
        /// <param name="declineReason">The reason for declining the registration.</param>
        /// <returns>A task that represents the asynchronous operation.</returns>
        Task DeclineCompanyAsync(Guid companyId, string declineReason);

        /// <summary>
        /// Gets the list of nevis companies with submissions.
        /// </summary>
        /// <returns>A <see cref="Task{TResult}"/> representing the asynchronous operation. The task result contains the list of nevis companies with submissions.</returns>
        Task<List<LegalEntity>> GetNevisCompaniesWithSubmissions();

        /// <summary>
        /// Gets the Nevis legal entities with master client users.
        /// </summary>
        /// <returns>A <see cref="Task{List}"/> representing the asynchronous operation.</returns>
        Task<List<LegalEntity>> GetNevisLegalEntitiesWithMasterClientUsersAsync();

        /// <summary>
        /// Gets the Nevis legal entities for the "submissions not paid" report.
        /// </summary>
        /// <returns>A <see cref="Task{TResult}"/> representing the asynchronous operation. The task result contains the list of legal entities.</returns>
        Task<IEnumerable<Submission>> GetNevisLegalEntitiesForSubmissionNotPaidReportAsync();

        /// <summary>
        /// Gets the annual fees for the given legal entity.
        /// </summary>
        /// <param name="companyId">The id of the company to get the fees for.</param>
        /// <returns>A <see cref="Task{TResult}"/> representing the asynchronous operation.</returns>
        Task<CompanyAnnualFeesDTO> GetCompanyAnnualFeeStatusesAsync(Guid companyId);

        /// <summary>
        /// Sets (creates or updates) wether the annual fee for a company is paid.
        /// </summary>
        /// <param name="companyId">The id of the company.</param>
        /// <param name="annualFees">Thelist with annual fees to set.</param>
        /// <returns>A <see cref="Task{TResult}"/> representing the asynchronous operation.</returns>
        Task SetCompanyAnnualFeeStatusAsync(Guid companyId, List<AnnualFeeDTO> annualFees);

        /// <summary>
        /// Gets the Bahamas legal entities for the "submissions not paid" report.
        /// </summary>
        /// <returns>A <see cref="Task{TResult}"/> representing the asynchronous operation. The task result contains the list of legal entities.</returns>
        Task<IEnumerable<LegalEntity>> GetBahamasLegalEntitiesForSubmissionNotPaidReportAsync();
    }
}