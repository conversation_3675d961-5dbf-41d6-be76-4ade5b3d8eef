using ClosedXML.Excel;
using NetProGroup.Trust.Domain.Submissions;
using NetProGroup.Trust.DataManager.Extensions;
using NetProGroup.Trust.Domain.Shared.Reports;

namespace NetProGroup.Trust.Reports.ExcelTemplateExporter.Populator
{
    /// <summary>
    /// Base class for line populator.
    /// </summary>
    public abstract class LinePopulatorBase
    {
        /// <summary>
        /// Gets abstract property for font color, to be specified by derived classes.
        /// </summary>
        protected abstract XLColor FontColor { get; }

        /// <summary>
        /// Gets abstract property for font size, to be specified by derived classes.
        /// </summary>
        protected abstract double FontSize { get; }

        /// <summary>
        /// Gets abstract property for font name, to be specified by derived classes.
        /// </summary>
        protected abstract string FontName { get; }

        /// <summary>
        /// Populates a cell with value, handles null values, and applies the specified styling.
        /// </summary>
        protected void SetCellValueAndStyle(IXLWorksheet worksheet, int row, int column, object value)
        {
            ArgumentNullException.ThrowIfNull(worksheet, nameof(worksheet));
            var cell = worksheet.Cell(row, column);
            cell.Value = ConvertToCellValue(value);
            ApplyFontStyle(cell);
        }

        /// <summary>
        /// Converts the object to a value type that can be assigned to an IXLCell.
        /// </summary>
        protected virtual XLCellValue ConvertToCellValue(object value)
        {
            return value switch
            {
                null => string.Empty,
                string str => str,
                int intValue => intValue.ToString(),
                double dblValue => dblValue.ToString("F"),
                DateTime dateTime => dateTime.ToString("MM/dd/yyyy"),
                bool boolValue => boolValue.ToString(),
                decimal decValue => decValue.ToString("F"),
                _ => value.ToString()
            };
        }

        /// <summary>
        /// Formats the utc time to the local tiem for the jurisdiction.
        /// </summary>
        /// <param name="dateTime"></param>
        /// <param name="submission"></param>
        /// <returns></returns>
        protected virtual string FormatDateAsLocalTime(DateTime? dateTime, Submission submission)
        {
            ArgumentNullException.ThrowIfNull(submission, nameof(submission));

            return FormatDateAsLocalTime(dateTime, submission.LegalEntity.Jurisdiction.Code);
        }

        /// <summary>
        /// Formats the UTC time to the local time for the jurisdiction using the DTO.
        /// </summary>
        /// <param name="dateTime">The date time to format.</param>
        /// <param name="dtoJurisdictionCode"></param>
        /// <returns>The formatted date string.</returns>
        protected virtual string FormatDateAsLocalTime(DateTime? dateTime, string dtoJurisdictionCode)
        {
            ArgumentNullException.ThrowIfNull(dtoJurisdictionCode);

            if (!dateTime.HasValue)
            {
                return string.Empty;
            }

            return dateTime.Value.ToLocalTime(dtoJurisdictionCode).ToString(WellKnownReportConstants.DateFormat);
        }

        /// <summary>
        /// Applies the specified font styling to the cell.
        /// </summary>
        private void ApplyFontStyle(IXLCell cell)
        {
            cell.Style.Font.FontName = FontName;
            cell.Style.Font.FontSize = FontSize;
            cell.Style.Font.FontColor = FontColor;
        }
    }
}