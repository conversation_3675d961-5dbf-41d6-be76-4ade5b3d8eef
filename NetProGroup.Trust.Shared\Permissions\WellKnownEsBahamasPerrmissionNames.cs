﻿// <copyright file="WellKnownEsBahamasPerrmissionNames.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

namespace NetProGroup.Trust.Domain.Shared.Permissions
{
    /// <summary>
    /// A list of the known permissions for the Economic Substance Bahamas module.
    /// </summary>
#pragma warning disable SA1202 // Elements should be ordered by access
#pragma warning disable SA1310 // Field names should not contain underscore
    public static partial class WellKnownPermissionNames
    {
        /// <summary>
        /// View STR submissions.
        /// </summary>
        public const string ESBahamasModule_Submissions_View = ESBahamasModule_Submissions + ".view";

        /// <summary>
        /// Search STR submissions.
        /// </summary>
        public const string ESBahamasModule_Submissions_Search = ESBahamasModule_Submissions + ".search";

        /// <summary>
        /// Export STR submissions.
        /// </summary>
        public const string ESBahamasModule_Submissions_Export = ESBahamasModule_Submissions + ".export";

        /// <summary>
        /// Reset STR submissions to Saved/Re-open.
        /// </summary>
        public const string ESBahamasModule_Submissions_Reset = ESBahamasModule_Submissions + ".reset";

        /// <summary>
        /// Delete completed STR submissions.
        /// </summary>
        public const string ESBahamasModule_Submissions_Delete_Completed = ESBahamasModule_Submissions + ".delete-completed";

        /// <summary>
        /// Delete saved STR submissions.
        /// </summary>
        public const string ESBahamasModule_Submissions_Delete_Saved = ESBahamasModule_Submissions + ".delete-saved";

        /// <summary>
        /// View paid/unpaid.
        /// </summary>
        public const string ESBahamasModule_Submissions_View_Paid = ESBahamasModule_Submissions + ".view-paid";

        /// <summary>
        /// Mark as paid.
        /// </summary>
        public const string ESBahamasModule_Submissions_Mark_Paid = ESBahamasModule_Submissions + ".mark-paid";

        /// <summary>
        /// Import payments.
        /// </summary>
        public const string ESBahamasModule_Payments_Import = ESBahamasModule + ".payments.import";

        /// <summary>
        /// Export submissions to ITA.
        /// </summary>
        public const string ESBahamasModule_Submissions_Export_ITA = ESBahamasModule_Submissions + ".export.ita";

        /// <summary>
        /// Export invoices.
        /// </summary>
        public const string ESBahamasModule_Invoices_Export = ESBahamasModule + ".invoices.export";

        /// <summary>
        /// View custom ES fee.
        /// </summary>
        public const string ESBahamasModule_Companies_View_Custom_ES_Fee = ESBahamasModule + "." + Companies + ".custom-es-fee.view";

        /// <summary>
        /// Set custom ES fee.
        /// </summary>
        public const string ESBahamasModule_Companies_Set_Custom_ES_Fee = ESBahamasModule + "." + Companies + ".custom-es-fee.set";

        /// <summary>
        /// Set financial period.
        /// </summary>
        public const string ESBahamasModule_Set_Financial_Period = ESBahamasModule + ".financial-period.set";

        /// <summary>
        /// View and run the data migration.
        /// </summary>
        public const string ESBahamasModule_DataMigration = ESBahamasModule + ".data-migration";

        private const string ESBahamasModule = "es.bahamas";
        private const string ESBahamasModule_Submissions = ESBahamasModule + ".submissions";
    }
#pragma warning restore SA1310 // Field names should not contain underscore
#pragma warning restore SA1202 // Elements should be ordered by access
}
