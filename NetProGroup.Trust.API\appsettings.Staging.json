{
  // Identifies the API to authenticate incoming authentication requests
  "AzureAd": {
    "Instance": "https://login.microsoftonline.com/",
    "TenantId": "4e469f54-cd7f-4db2-a879-55f2618bb8b3",
    "ClientId": "0e92f5d2-c439-4c14-ba69-6ee791aaf991",
    "AllowWebApiToBeAuthorizedByACL": true,
    "Audience": "api://0e92f5d2-c439-4c14-ba69-6ee791aaf991"
  },
  "AppRegistration": {
    "TenantId": "4e469f54-cd7f-4db2-a879-55f2618bb8b3",
    "ClientId": "0e92f5d2-c439-4c14-ba69-6ee791aaf991",
    "ClientSecret": "keyvault"
  },
  "ExternalId": {
    "ClientId": "cbe30ec4-b8ac-47dd-9495-7597bafa9ed8",
    "TenantId": "056ac2cd-bd90-4496-9857-265f01797639"
  },
  "BlobStorage": {
    "AccountName": "saaccpcpweu",
    "ContainerName": "documents"
  },
  "Azure": {
    "MSGraph": {
      "AD": {
        // Use defaults from AppRegistration
        "Scopes": ".default"
      }
    }
  },
  "Smtp": {
    "host": "je-smtp-outbound-1.mimecast-offshore.com",
    "port": 587,
    "secure": true,
    "username": "<EMAIL>",
    "password": "keyvault",
    "fromEmail": "<EMAIL>",
    "nameEmail": "TridentTrust Client Portal"
  },
  // These settings are for the trust office
  "TrustOffice": {
    "EmailDomain": "tridenttrust.com",
    "ProductionOfficeEmailSuffix": "_BO_Dir_Change_Request",
    "ClientPortalUrl": "https://clientfilings-acc.tridenttrust.com",
    "InvitationWhiteList": [
      "*@tridenttrust.com"
    ],
    "AllowedDomains": "tridenttrust.com",
    "RecipientOverride": {
      "ProductionOffice": "<EMAIL>",
      "Invitation": "<EMAIL>",
      "Announcement": "<EMAIL>"
    },
    "SendInvitationToUserEnabled": false
  },
  "DataMigration": {
    "Enabled": true,
    "ActiveJurisdiction": "Bahamas",
    "JobLockRefreshMarginSeconds": 290,
    "CountryOverrides": {
      "KNA": "St. Kitts and Nevis"
    },
    "JurisdictionSettings": {
      "Bahamas": {
        "MongoConnectionString": "mongodb://localhost:27017/",
        "MongoDatabaseName": "tbahclientportalaccdb",
        "Document": {
          "StorageAccountOptions": {
            "StorageAccounts": [
              {
                "Key": "SourceFileStorageBahamas",
                "AccountName": "tbahclientportalsa",
                "DefaultContainer": "tbah-clientportal-substance-acc-blobc"
              }
            ]
          }
        }
      }
    }
  },
  "DataSync": {
    "Enabled": true,
    "JurisdictionCodes": [
      "BS" // Bahamas
    ]
  },
  "FeatureFlags": {
    "Announcements": true
  },
  "JobScheduling": [
    {
      "Key": "reports.nevis.contacts-info",
      "CronExpression": "0 0 * * *",
      "Enabled": true
    },
    {
      "Key": "reports.nevis.companies-str-submission-status",
      "CronExpression": "0 5 * * *",
      "Enabled": true
    },
    {
      "Key": "sync.viewpoint",
      "CronExpression": "15 */2 * * *",
      "Enabled": true
    },
    {
      "Key": "reports.panama.basic-financial-report",
      "CronExpression": "0 0 * * *",
      "Enabled": false
    },
    {
      "Key": "reports.nevis.financial",
      "CronExpression": "0 * * * *",
      "Enabled": true
    },
    {
      "Key": "reports.nevis.submissions-not-paid",
      "CronExpression": "0 0 * * *",
      "Enabled": true
    }
  ]
}
