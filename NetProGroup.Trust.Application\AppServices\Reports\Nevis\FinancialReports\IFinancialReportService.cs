// <copyright file="IFinancialReportService.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

using NetProGroup.Framework.DependencyInjection.Interfaces;

namespace NetProGroup.Trust.Application.AppServices.Reports.Nevis.FinancialReports
{
    /// <summary>
    /// Interface for financial report service.
    /// </summary>
    public interface IFinancialReportService : ITransientService, IReportService;
}