﻿// <copyright file="ScheduledSubmissionsJob.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

using Microsoft.ApplicationInsights;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using NetProGroup.Trust.DataManager.Exceptions;
using NetProGroup.Trust.DataManager.Submissions.BVI.EconomicSubstance;
using NetProGroup.Trust.Domain.Scheduling;
using NetProGroup.Trust.Domain.Shared.Consts;

namespace NetProGroup.Trust.Application.Scheduler.Jobs.Submissions
{
    /// <summary>
    /// Scheduled job submitting scheduled submissions.
    /// </summary>
    public class ScheduledSubmissionsJob : JobBase<object>, IScheduledSubmissionsJob
    {
        private static bool _busy;

        /// <summary>
        /// Initializes a new instance of the <see cref="ScheduledSubmissionsJob"/> class.
        /// </summary>
        /// <param name="logger">The Logger.</param>
        /// <param name="serviceProvider">The Service Provider.</param>
        /// <param name="scheduledJobsRepository">The repository for ScheduledJobs.</param>
        /// <param name="telemetryClient">TelemetryClient instance.</param>
        /// <param name="options">The scheduling job settings options.</param>
        public ScheduledSubmissionsJob(ILogger<ScheduledSubmissionsJob> logger,
                                IServiceProvider serviceProvider,
                                IScheduledJobsRepository scheduledJobsRepository,
                                TelemetryClient telemetryClient, IOptions<ScheduledJobSettings> options)
        : base(logger, serviceProvider, options)
        {
        }

        /// <inheritdoc/>
        public Guid ScheduledJobId => new Guid(ScheduledJobConsts.SubmissionsJobId);

        /// <inheritdoc/>
        public string ScheduledJobKey => "submissions.scheduled";

        /// <inheritdoc/>
        public string ScheduledJobName => "Scheduled Submissions";

        /// <inheritdoc />
        protected override async Task DoWorkAsync(object data, CancellationToken token = default)
        {
            if (_busy)
            {
                Logger.LogDebug("Not running the scheduled submissions job because it is in progress already.");
                return;
            }

            var jobLock = await AcquireLockAsync(ScheduledJobId);

            if (!jobLock.Id.HasValue)
            {
                throw new NoLockException();
            }

            try
            {
                _busy = true;

                Logger.LogInformation("Starting scheduled submission tasks...");

                // Submit scheduled submissions for BVI
                var bviESSubmissionsDataManager = ServiceProvider.GetRequiredService<IBVIESSubmissionsDataManager>();

#pragma warning disable CA1031 // Do not catch general exception types
                try
                {
                    var submissions = await bviESSubmissionsDataManager.ListScheduledSubmissionsAsync(DateTime.Today);
                    foreach (var submission in submissions)
                    {
                        await bviESSubmissionsDataManager.SubmitScheduledSubmissionAsync(submission.Id);
                    }
                }
                catch (Exception ex)
                {
                    Logger.LogError(ex, "Handling scheduled submissions for BVI failed");
                }
#pragma warning restore CA1031 // Do not catch general exception types

                await Task.CompletedTask;
            }
            catch (LockNotFoundException)
            {
                Logger.LogError("The lock for the scheduled submissions job could no longer be found");
                jobLock = null;
            }
            finally
            {
                _busy = false;
                Logger.LogInformation("Scheduled submissions job completed");

                if (jobLock != null)
                {
                    await ReleaseLockAsync(jobLock);
                }
            }
        }
    }
}
