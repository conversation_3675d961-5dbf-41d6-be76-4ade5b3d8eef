// <copyright file="SubmissionsNotPaidReportJob.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using NetProGroup.Trust.Application.AppServices.Reports.Nevis.SubmissionsNotPaidReport;
using NetProGroup.Trust.Domain.Scheduling;

namespace NetProGroup.Trust.Application.Scheduler.Jobs.Reports
{
    /// <inheritdoc/>
    public class SubmissionsNotPaidReportJob(ILogger<SubmissionsNotPaidReportJob> logger, IServiceProvider serviceProvider, IOptions<ScheduledJobSettings> options)
        : JobBase<object>(logger, serviceProvider, options), ISubmissionsNotPaidReportJob
    {
        /// <inheritdoc/>
        public Guid ScheduledJobId => new Guid("{E3585FE3-3EE3-4711-B369-CA4A3A4A1FAB}");

        /// <inheritdoc/>
        public string ScheduledJobKey => "reports.nevis.submissions-not-paid";

        /// <inheritdoc/>
        public string ScheduledJobName => "Submissions Not Paid Report Job - Nevis";

        /// <inheritdoc/>
        protected override async Task DoWorkAsync(object data, CancellationToken token = default)
        {
            var jobLock = await base.AcquireLockAsync(ScheduledJobId);

            if (!jobLock.Id.HasValue)
            {
                throw new NoLockException();
            }

            try
            {
                var paymentReportService = ServiceProvider.GetRequiredService<ISubmissionsNotPaidReportService>();

                Logger.LogInformation("Starting report generation for {JobName}", ScheduledJobName);
                await paymentReportService.GenerateReportAsync(jobLock);
                Logger.LogInformation("Finished report generation for {JobName}", ScheduledJobName);
            }
            finally
            {
                await ReleaseLockAsync(jobLock);
            }
        }
    }
}