﻿// <copyright file="MessageMigrationService.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using MongoDB.Driver;
using NetProGroup.Framework.Services.Communication;
using NetProGroup.Trust.DataManager.Bulk;
using NetProGroup.Trust.DataManager.Configurations;
using NetProGroup.Trust.DataMigration.Factories;
using NetProGroup.Trust.DataMigration.Models.Bahamas;
using NetProGroup.Trust.Domain.Announcements;
using NetProGroup.Trust.Domain.Inboxes;
using NetProGroup.Trust.Domain.Jurisdictions;
using NetProGroup.Trust.Domain.LegalEntities;
using NetProGroup.Trust.Domain.MasterClients;
using NetProGroup.Trust.Domain.Repository;
using NetProGroup.Trust.Domain.Shared.Consts;
using NetProGroup.Trust.Domain.Shared.Roles;
using NetProGroup.Trust.Domain.Users;
using NetProGroup.Trust.DomainShared.Enums;
using NetProGroup.Trust.Shared.Jurisdictions;
using System.Collections;
using System.Text.RegularExpressions;

namespace NetProGroup.Trust.DataMigration.Services.Bahamas
{
    /// <summary>
    /// Service responsible for migrating <see cref="Message"/> data to the trust system.
    /// </summary>
    public class MessageMigrationService
    {
        private readonly ILogger<MessageMigrationService> _logger;
        private readonly IMasterClientsRepository _masterClientsRepository;
        private readonly IAnnouncementsRepository _announcementsRepository;
        private readonly IAnnouncementRecipientsRepository _announcementRecipientsRepository;
        private readonly IBulkOperationProvider _bulkOperationProvider;
        private readonly IInboxService _inboxService;
        private readonly IMongoDbFactory _mongoDbFactory;
        private readonly IApplicationUsersRepository _applicationUsersRepository;
        private readonly FileMigrationService _fileMigrationService;
        private readonly TrustDbContext _dbContext;
        private readonly List<(IEnumerable<object> Entities, string Type)> _pendingBulkInserts = new();
        private readonly TrustOfficeOptions _trustOfficeOptions;
        private readonly ILegalEntitiesRepository _legalEntitiesRepository;

        private const string Scheduled = "SCHEDULED";
        private const string InProgress = "IN PROGRESS";
        private const string Sent = "SENT";

        /// <summary>
        /// Initializes a new instance of the <see cref="MessageMigrationService"/> class.
        /// This service is responsible for migrating legacy <see cref="Message"/> data from MongoDB
        /// into the trust system, creating corresponding <see cref="Announcement"/> entities,
        /// recipients, inbox items, and updating their statuses.
        /// </summary>
        /// <param name="logger">The logger instance used for diagnostic and trace logging.</param>
        /// <param name="masterClientsRepository">Repository to access and validate master client entities.</param>
        /// <param name="announcementsRepository">Repository to create and update announcements.</param>
        /// <param name="dbContext">The Entity Framework database context used for transactional operations.</param>
        /// <param name="announcementRecipientsRepository">Repository to manage announcement recipients.</param>
        /// <param name="bulkOperationProvider">Service that performs high-performance bulk insert operations.</param>
        /// <param name="inboxService">Service to create and manage inbox items associated with announcements.</param>
        /// <param name="mongoDbFactory">Factory used to obtain MongoDB database instances for data retrieval.</param>
        /// <param name="applicationUsersRepository">The Application Users Repository.</param>
        /// <param name="fileMigrationService">The file migration service.</param>
        /// <param name="trustOfficeOptions">Configuration for TrustOffice.</param>
        /// <param name="legalEntitiesRepository">Repository to get Legal Entities data.</param>
        /// <exception cref="ArgumentNullException">Thrown if any of the injected dependencies are <c>null</c>.</exception>
        public MessageMigrationService(
            ILogger<MessageMigrationService> logger,
            IMasterClientsRepository masterClientsRepository,
            IAnnouncementsRepository announcementsRepository,
            TrustDbContext dbContext,
            IAnnouncementRecipientsRepository announcementRecipientsRepository,
            IBulkOperationProvider bulkOperationProvider,
            IInboxService inboxService,
            IMongoDbFactory mongoDbFactory,
            IApplicationUsersRepository applicationUsersRepository,
            FileMigrationService fileMigrationService,
            IOptions<TrustOfficeOptions> trustOfficeOptions,
            ILegalEntitiesRepository legalEntitiesRepository)
        {
            ArgumentNullException.ThrowIfNull(trustOfficeOptions, nameof(trustOfficeOptions));
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
            _masterClientsRepository = masterClientsRepository ?? throw new ArgumentNullException(nameof(masterClientsRepository));
            _announcementsRepository = announcementsRepository ?? throw new ArgumentNullException(nameof(announcementsRepository));
            _dbContext = dbContext ?? throw new ArgumentNullException(nameof(dbContext));
            _announcementRecipientsRepository = announcementRecipientsRepository ?? throw new ArgumentNullException(nameof(announcementRecipientsRepository));
            _bulkOperationProvider = bulkOperationProvider ?? throw new ArgumentNullException(nameof(bulkOperationProvider));
            _inboxService = inboxService ?? throw new ArgumentNullException(nameof(inboxService));
            _mongoDbFactory = mongoDbFactory ?? throw new ArgumentNullException(nameof(mongoDbFactory));
            _fileMigrationService = fileMigrationService ?? throw new ArgumentNullException(nameof(fileMigrationService));
            _applicationUsersRepository = applicationUsersRepository ?? throw new ArgumentNullException(nameof(applicationUsersRepository));
            _trustOfficeOptions = trustOfficeOptions.Value;
            _legalEntitiesRepository = legalEntitiesRepository ?? throw new ArgumentNullException(nameof(legalEntitiesRepository));
        }

        /// <summary>
        /// Handles the migration of a <see cref="Message"/> to an <see cref="Announcement"/>.
        /// </summary>
        /// <param name="message">The message.</param>
        /// <param name="jurisdictionId">The unique identifier of the jurisdiction.</param>
        /// <returns>The success flag and the list of errors.</returns>
        public async Task<(bool Success, List<string> Errors)> HandleMessage(Message message, Guid jurisdictionId)
        {
            ArgumentNullException.ThrowIfNull(message);
            var errors = ValidateMessage(message);
            if (errors.Any())
                return (false, errors);

            List<MasterClient> masterClients = new();
            if (message.MasterClientCodes != null && message.MasterClientCodes.Any())
            {
                masterClients = (await _masterClientsRepository
                    .FindByConditionAsync(mc => message.MasterClientCodes.Contains(mc.Code)))
                    .ToList();
            }

            ValidateMasterClientsAsync(message, masterClients, errors);
            if (errors.Any())
                return (false, errors);

            var masterClientMessages = await GetMasterClientMessagesAsync(message.Id, errors);

            await using var transaction = await _dbContext.Database.BeginTransactionAsync();
            try
            {
                var announcement = await CreateAnnouncementAsync(message, masterClientMessages, errors);
                if (errors.Any())
                    return (false, errors);

                var recipients = await AddRecipientsAsync(announcement.Id, message, jurisdictionId, masterClients);

                var success = await UpdateAnnouncementStatusAsync(announcement, message.Status, masterClientMessages, recipients, errors);
                if (!success)
                {
                    errors.Add($"Unknown message status: {message.Status}");
                    await transaction.RollbackAsync();
                    return (false, errors);
                }

                await _dbContext.SaveChangesAsync();

                await _fileMigrationService.MigrateFiles(announcement, message, errors);

                foreach (var (entities, type) in _pendingBulkInserts)
                {
                    await _bulkOperationProvider.BulkInsertAsync((dynamic)entities, _dbContext);
                    _logger.LogTrace("BulkInsert executed for {EntityType}, Count {Count}",
                        type, ((ICollection)entities).Count);
                }

                if (errors.Any())
                {
                    await transaction.RollbackAsync();
                    return (false, errors);
                }

                await transaction.CommitAsync();
                return (true, errors);
            }
            catch (Exception ex)
            {
                await transaction.RollbackAsync();
                _logger.LogError(ex, "Error while migrating message {MessageId}", message.Id);
                throw;
            }
        }

        #region Private Helpers

        /// <summary>
        /// Validates the message fields.
        /// </summary>
        /// <param name="message">The message to validate.</param>
        /// <returns>The list of errors.</returns>
        private static List<string> ValidateMessage(Message message)
        {
            var errors = new List<string>();

            if (string.IsNullOrWhiteSpace(message.Content))
                errors.Add("Message content is empty.");

            if (string.IsNullOrWhiteSpace(message.Subject))
                errors.Add("Message subject is empty.");

            if (string.IsNullOrWhiteSpace(message.EmailSubject))
                errors.Add("Message email subject is empty.");

            if (!message.SendToAll && (message.MasterClientCodes == null || !message.MasterClientCodes.Any()))
                errors.Add("Message has no associated master client codes.");

            return errors;
        }

        /// <summary>
        /// Validates that all master client codes in the message exist in the system.
        /// </summary>
        /// <param name="message">The message.</param>
        /// <param name="masterClients">The master clients.</param>
        /// <param name="errors">The list of errors.</param>
        /// <returns>The result of the task.</returns>
        private static void ValidateMasterClientsAsync(Message message, IReadOnlyList<MasterClient> masterClients, List<string> errors)
        {
            if (message.MasterClientCodes == null || !message.MasterClientCodes.Any())
                return;

            foreach (var missingCode in message.MasterClientCodes.Except(masterClients.Select(mc => mc.Code)))
            {
                errors.Add($"Master client with code {missingCode} not found.");
            }
        }

        /// <summary>
        /// Extracts the company code from the email subject using a regular expression.
        /// </summary>
        /// <param name="emailSubject">The Email Subject.</param>
        /// <returns>The Company code.</returns>
        private static string ExtractCompanyCodeFromSubject(string emailSubject)
        {
            if (string.IsNullOrWhiteSpace(emailSubject))
                return null;

            var subject = emailSubject.Replace("\\r\\n", "\r\n", StringComparison.OrdinalIgnoreCase).Replace("\\n", "\n", StringComparison.OrdinalIgnoreCase);
            var match = Regex.Match(
                subject,
                @"Economic Substance Request\s*[-–—]\s*([^\s\.\r\n]+)",
                RegexOptions.IgnoreCase | RegexOptions.CultureInvariant
            );

            if (match.Success)
                return match.Groups[1].Value.Trim();

            // Fallback to simple string manipulation if regex fails
            const string prefix = "Economic Substance Request - ";
            var idx = subject.IndexOf(prefix, StringComparison.OrdinalIgnoreCase);
            if (idx >= 0)
            {
                idx += prefix.Length;
                var rest = subject.Substring(idx);
                var terminators = new[] { '\r', '\n', '.', ' ' };
                var end = rest.IndexOfAny(terminators);
                return end > 0 ? rest.Substring(0, end).Trim() : rest.Trim();
            }

            return null;
        }

        /// <summary>
        /// Extracts the company code from the URLs associated with the message.
        /// </summary>
        /// <param name="urls">The url schemas from the message.</param>
        /// <returns>The Company code.</returns>
        private static string ExtractCompanyCodeFromUrls(IEnumerable<UrlSchema> urls)
        {
            var companySubmission = urls?
                .FirstOrDefault(u => u?.Name != null &&
                                     u.Name.Equals("Company submissions", StringComparison.OrdinalIgnoreCase));

            if (companySubmission?.Url == null)
                return null;

            var segments = companySubmission.Url.Segments
                .Select(s => s.Trim('/'))
                .Where(s => !string.IsNullOrWhiteSpace(s))
                .ToArray();

            for (int i = 0; i < segments.Length - 1; i++)
            {
                if (string.Equals(segments[i], "companies", StringComparison.OrdinalIgnoreCase))
                {
                    var code = segments[i + 1]?.Trim('/');
                    return string.IsNullOrWhiteSpace(code) ? null : code;
                }
            }

            return null;
        }

        /// <summary>
        /// Builds the body of the announcement from the message content and URLs.
        /// </summary>
        /// <param name="message">The message.</param>
        /// <param name="errors">The list of errors.</param>
        /// <returns>The body of the announcement.</returns>
        private async Task<string> BuildAnnouncementBody(Message message, List<string> errors)
        {
            if (message.Urls == null || !message.Urls.Any())
                return message.Content;

            var portalUrl = _trustOfficeOptions.ClientPortalUrl;
            if (string.IsNullOrWhiteSpace(portalUrl))
            {
                errors.Add("Client portal URL is not configured.");
                return message.Content;
            }

            var code = ExtractCompanyCodeFromSubject(message.EmailSubject);

            if (string.IsNullOrWhiteSpace(code))
            {
                code = ExtractCompanyCodeFromUrls(message.Urls);
            }

            if (string.IsNullOrWhiteSpace(code))
            {
                errors.Add("Could not extract company code from email subject or URLs.");
                return message.Content;
            }

            var legalEntity = await _legalEntitiesRepository
                .FindFirstOrDefaultByConditionAsync(le => le.Code == code);

            if (legalEntity == null)
            {
                errors.Add($"Legal entity with code {code} not found.");
                return message.Content;
            }

            var finalUrl = $"{portalUrl.TrimEnd('/')}/economic-substance/submissions?setCompanyId={legalEntity.Id}&setMasterClientId={legalEntity.MasterClientId}";
            return $"{message.Content}{Environment.NewLine}{Environment.NewLine}{finalUrl}";
        }

        /// <summary>
        /// Retrieves the master client message from the MongoDB database based on the provided message ID.
        /// </summary>
        /// <param name="messageId">The unique identifier of the message.</param>
        /// <param name="errors">The list of errors.</param>
        /// <returns>The MasterClientMessages.</returns>
        private async Task<IReadOnlyList<MasterClientMessage>> GetMasterClientMessagesAsync(string messageId, List<string> errors)
        {
            var mongoDatabase = _mongoDbFactory.GetMongoDatabase(JurisdictionCodes.Bahamas);
            var masterClientMessages = await mongoDatabase
                .GetCollection<MasterClientMessage>("masterclientmessages")
                .Find(mcm => mcm.MessageId == messageId)
                .ToListAsync();

            if (masterClientMessages == null || masterClientMessages.Count == 0)
            {
                _logger.LogError("No MasterClientMessages found for MessageId {MessageId}", messageId);
            }
            else
            {
                _logger.LogTrace("Found {Count} MasterClientMessages for MessageId {MessageId}",
                    masterClientMessages.Count, messageId);
            }

            return masterClientMessages;
        }

        /// <summary>
        /// Creates a new <see cref="Announcement"/> based on the provided <see cref="Message"/>.
        /// </summary>
        /// <param name="message">The message.</param>
        /// <param name="masterClientMessages">The master client messages.</param>
        /// <param name="errors">The list of errors.</param>
        /// <returns>The created Announcement.</returns>
        private async Task<Announcement> CreateAnnouncementAsync(Message message, IReadOnlyList<MasterClientMessage> masterClientMessages, List<string> errors)
        {
            DateTime? sentAt = (masterClientMessages == null) || masterClientMessages.Count == 0 ? null : masterClientMessages
                .Where(m => m.SentAt.HasValue)
                .OrderBy(m => m.SentAt)
                .Select(m => m.SentAt)
                .FirstOrDefault();

            var announcement = new Announcement
            {
                Subject = message.Subject,
                EmailSubject = message.EmailSubject,
                Body = await BuildAnnouncementBody(message, errors),
                SendAt = message.ScheduledAt,
                SentAt = sentAt
            };

            if (errors.Any())
                return null;

            _logger.LogTrace("Creating new Announcement with subject {Subject}", message.Subject);
            await _announcementsRepository.InsertAsync(announcement, false);

            return announcement;
        }

        /// <summary>
        /// Updates the status of the announcement based on the message status.
        /// </summary>
        /// <param name="announcement">The Announcement.</param>
        /// <param name="status">The status of the Announcement.</param>
        /// <param name="masterClientMessages">The Master client messages.</param>
        /// <param name="recipients">The list of recipients.</param>
        /// <param name="errors">The list of errors.</param>
        /// <returns>The flag indicating the result of the operation.</returns>
        private async Task<bool> UpdateAnnouncementStatusAsync(
            Announcement announcement,
            string status,
            IReadOnlyList<MasterClientMessage> masterClientMessages,
            List<AnnouncementRecipient> recipients,
            List<string> errors)
        {
            return status switch
            {
                Scheduled => await HandleScheduledAsync(announcement),
                InProgress => await HandleScheduledAsync(announcement), // InProgress is treated as Scheduled
                Sent => await HandleSentAsync(announcement, masterClientMessages, recipients, errors),
                _ => false
            };
        }

        /// <summary>
        /// Handles the scheduled status of the announcement.
        /// </summary>
        /// <param name="announcement"></param>
        /// <returns></returns>
        private async Task<bool> HandleScheduledAsync(Announcement announcement)
        {
            announcement.Status = AnnouncementStatus.Scheduled;
            await _announcementsRepository.UpdateAsync(announcement, false);
            _logger.LogTrace("Scheduled Announcement created with id {AnnouncementId}", announcement.Id);
            return true;
        }

        /// <summary>
        /// Handles the sent status of the announcement by creating an inbox item and updating the announcement status.
        /// </summary>
        /// <param name="announcement">The announcement.</param>
        /// <param name="masterClientMessages">The Master Client Messages.</param>
        /// <param name="recipients">The list of recipients.</param>
        /// <param name="errors">The list of errors.</param>
        /// <returns>The result flag.</returns>
        private async Task<bool> HandleSentAsync(
            Announcement announcement,
            IReadOnlyList<MasterClientMessage> masterClientMessages,
            List<AnnouncementRecipient> recipients,
            List<string> errors)
        {
            if (masterClientMessages == null || masterClientMessages.Count == 0)
            {
                _logger.LogWarning("No MasterClientMessages found for Announcement {AnnouncementId}", announcement.Id);
                return false;
            }

            await CreateInboxAsync(announcement, masterClientMessages, recipients, errors);
            _logger.LogTrace("Sent Announcement created with id {AnnouncementId}", announcement.Id);
            return true;
        }

        /// <summary>
        /// Creates an inbox item for the announcement and adds recipients to it.
        /// </summary>
        /// <param name="announcement">The announcement.</param>
        /// <param name="masterClientMessages">The Master Client Messages.</param>
        /// <param name="recipients">The list of recipients.</param>
        /// <param name="errors">The list of errors.</param>
        /// <returns>The result of the task.</returns>
        private async Task CreateInboxAsync(
            Announcement announcement,
            IReadOnlyList<MasterClientMessage> masterClientMessages,
            List<AnnouncementRecipient> recipients,
            List<string> errors)
        {
            var inboxId = await _inboxService.CreateInboxItemAsync(
                UserConsts.SystemUserId,
                WellKnownRoleIds.System,
                UserConsts.InboxUserId,
                null,
                announcement.Subject,
                announcement.Body,
                null,
                true);

            var inboxOwners = new List<InboxOwner>();
            foreach (var recipient in recipients)
            {
                inboxOwners.Add(new InboxOwner
                {
                    InboxId = inboxId,
                    OwnerId = recipient.RecipientId,
                    Type = recipient.Type,
                });

                _logger.LogTrace("Created InboxOwner for RecipientId {RecipientId}, Type {Type}, InboxId {InboxId}",
                    recipient.RecipientId, recipient.Type, inboxId);
            }

            if (inboxOwners.Count > 0)
                _pendingBulkInserts.Add((inboxOwners, nameof(InboxOwner)));

            var openedItems = masterClientMessages
                .Where(m => !string.IsNullOrWhiteSpace(m.OpenedBy) && m.OpenedAt.HasValue)
                .ToList();

            if (openedItems.Count > 0)
            {
                var normalizedEmails = openedItems
                    .Select(i => i.OpenedBy?.Trim())
                    .Where(email => !string.IsNullOrWhiteSpace(email))
                    .Select(email => email!.ToUpperInvariant())
                    .Distinct()
                    .ToList();

                if (normalizedEmails.Count == 0)
                {
                    _logger.LogWarning("No valid OpenedBy emails found in MasterClientMessages for InboxReadStatus creation");
                    errors.Add("No valid OpenedBy emails found in MasterClientMessages for InboxReadStatus creation");
                    return;
                }

                var usersList = await _applicationUsersRepository.FindByConditionAsync(au => normalizedEmails.Contains(au.NormalizedEmail));
                var users = usersList.ToDictionary(u => u.NormalizedEmail, u => u);

                var inboxReadStatuses = new List<InboxReadStatus>();

                foreach (var item in openedItems)
                {
                    var email = item.OpenedBy?.Trim();
                    if (string.IsNullOrWhiteSpace(email))
                    {
                        _logger.LogWarning("OpenedBy email is null or empty for MasterClientMessage with Id {Id}", item.Id);
                        errors.Add($"OpenedBy email is null or empty for MasterClientMessage with Id {item.Id}");
                        continue;
                    }

                    var normalizedEmail = email.ToUpperInvariant();

                    if (!users.TryGetValue(normalizedEmail, out var user))
                    {
                        _logger.LogWarning("User with email {Email} not found for InboxReadStatus creation", normalizedEmail);
                        errors.Add($"User with email {email} not found for InboxReadStatus creation");
                        continue;
                    }

                    inboxReadStatuses.Add(new InboxReadStatus
                    {
                        InboxId = inboxId,
                        UserId = user.Id,
                        ReadAt = item.OpenedAt!.Value
                    });
                }

                if (inboxReadStatuses.Count > 0)
                {
                    _pendingBulkInserts.Add((inboxReadStatuses, nameof(InboxReadStatus)));
                    _logger.LogTrace("Created {Count} InboxReadStatus entries for InboxId {InboxId}",
                        inboxReadStatuses.Count, inboxId);
                }
            }

            announcement.Status = AnnouncementStatus.Sent;
            await _announcementsRepository.UpdateAsync(announcement, false);
        }

        /// <summary>
        /// Adds recipients to the announcement based on the message settings.
        /// </summary>
        /// <param name="announcementId">The unique identifier of the announcement.</param>
        /// <param name="message">The message.</param>
        /// <param name="jurisdictionId">The unique identifier of the jurisdiction.</param>
        /// <param name="masterClients">The master clients.</param>
        /// <returns>The result of the task.</returns>
        private async Task<List<AnnouncementRecipient>> AddRecipientsAsync(Guid announcementId, Message message, Guid jurisdictionId, IReadOnlyList<MasterClient> masterClients)
        {
            _logger.LogTrace("Configuring recipients for Announcement {AnnouncementId}", announcementId);

            if (message.SendToAll)
            {
                return await AddJurisdictionRecipientAsync(announcementId, jurisdictionId);
            }
            else
            {
                return AddMasterClientRecipientsAsync(announcementId, masterClients);
            }
        }

        /// <summary>
        /// Adds a jurisdiction recipient to the announcement if it does not already exist.
        /// </summary>
        /// <param name="announcementId">The unique identifier of the announcement.</param>
        /// <param name="jurisdictionId">The unique identifier of the jurisdiction.</param>
        /// <returns>The result of the task.</returns>
        private async Task<List<AnnouncementRecipient>> AddJurisdictionRecipientAsync(Guid announcementId, Guid jurisdictionId)
        {
            List<AnnouncementRecipient> newRecipients = new();
            const string type = nameof(Jurisdiction);
            var recipient = new AnnouncementRecipient
            {
                AnnouncementId = announcementId,
                Type = type,
                RecipientId = jurisdictionId
            };

            newRecipients.Add(recipient);
            await _announcementRecipientsRepository.InsertAsync(recipient, false);
            _logger.LogTrace("Created AnnouncementRecipient for Jurisdiction {JurisdictionId}, Announcement {AnnouncementId}", jurisdictionId, announcementId);
            return newRecipients;
        }

        /// <summary>
        /// Adds master client recipients to the announcement based on their codes.
        /// </summary>
        /// <param name="announcementId">The unique identifier of the announcement.</param>
        /// <param name="masterClients">The master clients.</param>
        /// <returns>The result of the task.</returns>
        private List<AnnouncementRecipient> AddMasterClientRecipientsAsync(Guid announcementId, IReadOnlyList<MasterClient> masterClients)
        {
            const string type = nameof(MasterClient);
            var masterClientIds = masterClients.Select(mc => mc.Id).ToList();

            var newRecipients = masterClientIds
                .Select(id => new AnnouncementRecipient(Guid.NewGuid())
                {
                    AnnouncementId = announcementId,
                    Type = type,
                    RecipientId = id
                })
                .ToList();

            if (newRecipients.Any())
            {
                _pendingBulkInserts.Add((newRecipients, nameof(AnnouncementRecipient)));
                _logger.LogTrace("Created {Count} AnnouncementRecipients for Announcement {AnnouncementId}",
                    newRecipients.Count, announcementId);
            }

            return newRecipients;
        }

        #endregion
    }
}


