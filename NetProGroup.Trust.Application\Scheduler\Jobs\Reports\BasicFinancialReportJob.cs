// <copyright file="BasicFinancialReportJob.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using NetProGroup.Trust.Application.AppServices.Reports.Panama.BasicFinancialReport;
using NetProGroup.Trust.Domain.Scheduling;

namespace NetProGroup.Trust.Application.Scheduler.Jobs.Reports
{
    /// <inheritdoc/>
    public class BasicFinancialReportJob(ILogger<BasicFinancialReportJob> logger, IServiceProvider serviceProvider, IOptions<ScheduledJobSettings> options)
        : JobBase<object>(logger, serviceProvider, options), IBasicFinancialReportJob
    {
        /// <inheritdoc/>
        public Guid ScheduledJobId => new Guid("{c78bdab5-61c0-4a00-87f6-569bee378da0}");

        /// <inheritdoc/>
        public string ScheduledJobKey => "reports.panama.basic-financial-report";

        /// <inheritdoc/>
        public string ScheduledJobName => "Basic Financial Report Job - Panama";

        /// <inheritdoc/>
        protected override async Task DoWorkAsync(object data, CancellationToken token = default)
        {
            var jobLock = await base.AcquireLockAsync(ScheduledJobId);

            if (!jobLock.Id.HasValue)
            {
                throw new NoLockException();
            }

            try
            {
                var financialReportService = ServiceProvider.GetRequiredService<IBasicFinancialReportService>();

                Logger.LogInformation("Starting report generation for {JobName}", ScheduledJobName);
                await financialReportService.GenerateReportAsync(jobLock);
                Logger.LogInformation("Finished report generation for {JobName}", ScheduledJobName);
            }
            finally
            {
                await ReleaseLockAsync(jobLock);
            }
        }
    }
}
