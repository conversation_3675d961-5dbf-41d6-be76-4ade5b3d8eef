// <copyright file="SubmissionsNotPaidReportService.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

using Microsoft.Extensions.Logging;
using NetProGroup.Framework.Services.Documents;
using NetProGroup.Framework.Services.Locks;
using NetProGroup.Framework.Services.Locks.Models;
using NetProGroup.Trust.Application.Contracts.Common;
using NetProGroup.Trust.DataManager.Reports;
using NetProGroup.Trust.Domain.Report;
using NetProGroup.Trust.Domain.Report.Enum;
using NetProGroup.Trust.Reports.Nevis.SubmissionsNotPaid;

namespace NetProGroup.Trust.Application.AppServices.Reports.Nevis.SubmissionsNotPaidReport
{
    /// <summary>
    /// The submissions not paid report service implementation.
    /// </summary>
    public class SubmissionsNotPaidReportService : ISubmissionsNotPaidReportService
    {
        private readonly ISubmissionsNotPaidReportGenerator _submissionsNotPaidReportGenerator;
        private readonly IReportsDataManager _reportDataManager;
        private readonly IDocumentManager _documentManager;
        private readonly ILockManager _lockManager;
        private readonly ILogger _logger;
        private readonly IDateTimeProvider _dateTimeProvider;

        /// <summary>
        /// Initializes a new instance of the <see cref="SubmissionsNotPaidReportService"/> class.
        /// </summary>
        /// <param name="logger">The logger.</param>
        /// <param name="dateTimeProvider">The datetime provider.</param>
        /// <param name="submissionsNotPaidReportGenerator">The payment report generator.</param>
        /// <param name="reportDataManager">The report data manager.</param>
        /// <param name="documentManager">The document manager.</param>
        /// <param name="lockManager">The lock manager.</param>
        public SubmissionsNotPaidReportService(
            ILogger<SubmissionsNotPaidReportService> logger,
            IDateTimeProvider dateTimeProvider,
            ISubmissionsNotPaidReportGenerator submissionsNotPaidReportGenerator,
            IReportsDataManager reportDataManager,
            IDocumentManager documentManager,
            ILockManager lockManager)
        {
            _submissionsNotPaidReportGenerator = submissionsNotPaidReportGenerator;
            _reportDataManager = reportDataManager;
            _documentManager = documentManager;
            _lockManager = lockManager;
            _logger = logger;
            _dateTimeProvider = dateTimeProvider;
        }

        /// <inheritdoc/>
        public async Task GenerateReportAsync(LockDTO jobLock)
        {
            ArgumentNullException.ThrowIfNull(jobLock);

            await _lockManager.RefreshLockAsync(jobLock.Id.Value);

            var fileTemplate = _submissionsNotPaidReportGenerator.GenerateReportNameForSubmissionsNotPaidAsync();
            var fileName = $"{fileTemplate}.xlsx";

            if (await _reportDataManager.ReportExists(new Report
            {
                ReportName = fileTemplate,
                Type = ReportType.SubmissionsNotPaid
            }))
            {
                return;
            }

            var submissionsNotPaidReportOutput = await _submissionsNotPaidReportGenerator.GenerateSubmissionsNotPaidReportAsync();

            if (!submissionsNotPaidReportOutput.LegalEntities.Any())
            {
                _logger.LogInformation("Submissions Not Paid Report couldn't find any data for the date: {Date}",
                    _dateTimeProvider.Now.ToString("yyyy-MM-dd"));
                return;
            }

            var documentId = await _documentManager.CreateDocumentAsync(
                documentType: (int)ReportType.SubmissionsNotPaid,
                description: fileTemplate,
                fileName: fileName,
                data: submissionsNotPaidReportOutput.FileContent,
                saveChanges: false);

            await _reportDataManager.AddReport(new Report
            {
                DocumentId = documentId,
                ReportName = fileTemplate,
                Type = ReportType.SubmissionsNotPaid
            });

            await _lockManager.RefreshLockAsync(jobLock.Id.Value);
        }
    }
}