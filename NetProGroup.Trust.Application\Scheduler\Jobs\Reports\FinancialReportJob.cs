// <copyright file="FinancialReportJob.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using NetProGroup.Trust.Application.AppServices.Reports.Nevis.FinancialReports;
using NetProGroup.Trust.Domain.Scheduling;

namespace NetProGroup.Trust.Application.Scheduler.Jobs.Reports
{
    /// <inheritdoc/>
    public class FinancialReportJob(ILogger<FinancialReportJob> logger, IServiceProvider serviceProvider, IOptions<ScheduledJobSettings> options)
        : JobBase<object>(logger, serviceProvider, options), IFinancialReportJob
    {
        /// <inheritdoc/>
        public Guid ScheduledJobId => new Guid("{4A6C3426-0DB7-4966-825A-AF08A9DB10A4}");

        /// <inheritdoc/>
        public string ScheduledJobKey => "reports.nevis.financial";

        /// <inheritdoc/>
        public string ScheduledJobName => "Financial Reporting Job - Nevis";

        /// <inheritdoc/>
        protected override async Task DoWorkAsync(object data, CancellationToken token = default)
        {
            var jobLock = await base.AcquireLockAsync(ScheduledJobId);

            if (!jobLock.Id.HasValue)
            {
                throw new NoLockException();
            }

            try
            {
                var financialReportService = ServiceProvider.GetRequiredService<IFinancialReportService>();

                Logger.LogInformation("Starting report generation for {JobName}", ScheduledJobName);
                await financialReportService.GenerateReportAsync(jobLock);
                Logger.LogInformation("Finished report generation for {JobName}", ScheduledJobName);
            }
            finally
            {
                await ReleaseLockAsync(jobLock);
            }
        }
    }
}
