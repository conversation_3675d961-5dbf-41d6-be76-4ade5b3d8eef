﻿using Azure.Identity;
using Azure.Storage;
using Azure.Storage.Blobs;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using NetProGroup.Framework.Services.Documents;
using NetProGroup.Framework.Services.Documents.EFRepository;
using NetProGroup.Trust.DataMigration.Models.Bahamas;
using NetProGroup.Trust.Domain.Announcements;
using NetProGroup.Trust.Domain.Forms;
using NetProGroup.Trust.Domain.Submissions;
using NetProGroup.Trust.Shared.Enums;

namespace NetProGroup.Trust.DataMigration.Services.Bahamas
{
    /// <summary>
    /// The file migration service.
    /// </summary>
    public sealed class FileMigrationService
    {
        private readonly ILogger<FileMigrationService> _logger;
        private readonly IOptions<DocumentOptions> _documentOptions;
        private readonly IDocumentRepository _documentRepository;
        private readonly IFormDocumentDocumentsRepository _formDocumentDocumentsRepository;
        private readonly IAnnouncementDocumentsRepository _announcementDocumentsRepository;
        private BlobContainerClient _sourceBlobStorage;
        private BlobContainerClient _targetBlobStorage;

        /// <summary>
        /// Initializes the file migration service.
        /// </summary>
        /// <param name="logger">The logger.</param>
        /// <param name="documentOptions">Blob storage config.</param>
        /// <param name="documentRepository">The document repository.</param>
        /// <param name="formDocumentDocumentsRepository">The form document document repository.</param>
        /// <param name="announcementDocumentsRepository">The announcements repository.</param>
        public FileMigrationService(
            ILogger<FileMigrationService> logger,
            IOptions<DocumentOptions> documentOptions,
            IDocumentRepository documentRepository,
            IFormDocumentDocumentsRepository formDocumentDocumentsRepository,
            IAnnouncementDocumentsRepository announcementDocumentsRepository)
        {
            _logger = logger;
            _documentOptions = documentOptions;
            _documentRepository = documentRepository;
            _formDocumentDocumentsRepository = formDocumentDocumentsRepository;
            _announcementDocumentsRepository = announcementDocumentsRepository;
        }

        /// <summary>
        /// Migrates the RFI files.
        /// </summary>
        /// <param name="submission">The submission.</param>
        /// <param name="rfi">The RFI info.</param>
        /// <param name="files">The files to migrate.</param>
        /// <param name="errors">The current errors.</param>
        /// <returns>An awaitable task.</returns>
        public async Task MigrateFiles(Submission submission, RequestForInformation rfi, List<FileSchema> files, List<string> errors)
        {
            ArgumentNullException.ThrowIfNull(submission, nameof(submission));
            ArgumentNullException.ThrowIfNull(rfi, nameof(rfi));
            ArgumentNullException.ThrowIfNull(errors, nameof(errors));

            if (files == null || files.Count == 0)
            {
                return;
            }

            EnsureBlobConnections();

            foreach (var item in files)
            {
                string targetFilePath = $"Submissions/{submission.Id}/RFI/{rfi.Id}/{item.BlobName}";

                var document = await UploadAndRegisterDocument(targetFilePath, item);

                if (document == null)
                {
                    _logger.LogWarning("Unable to copy {ItemUrl} because it cannot be found (SubmissionID: {SubmissionId}, RequestForInformationId: {RfiId}).", item.Url, submission.Id, rfi.Id);
                    errors.Add($"Unable to copy {item.Url} because it cannot be found  (SubmissionID: {submission.Id}, RequestForInformationId: {rfi.Id}).");
                    continue;
                }

                // Check if document is new or not:
                if (document.Id == Guid.Empty)
                {
                    rfi.Documents.Add(new RequestForInformationDocument
                    {
                        Document = document,
                        RequestForInformation = rfi,
                    });
                }
            }

            await _documentRepository.SaveChangesAsync();
        }

        /// <summary>
        /// Migrates the announcement related files.
        /// </summary>
        /// <param name="announcement">The announcement.</param>
        /// <param name="message">The message.</param>
        /// <param name="errors">The current errors.</param>
        /// <returns>An awaitable task.</returns>
        public async Task MigrateFiles(Announcement announcement, Message message, List<string> errors)
        {
            ArgumentNullException.ThrowIfNull(announcement, nameof(announcement));
            ArgumentNullException.ThrowIfNull(message, nameof(message));
            ArgumentNullException.ThrowIfNull(errors, nameof(errors));

            if (message.Files == null || message.Files.Count == 0)
            {
                return;
            }

            EnsureBlobConnections();

            foreach (var item in message.Files)
            {
                string targetFilePath = $"Announcements/{announcement.Id}/{item.BlobName}";
                var document = await UploadAndRegisterDocument(targetFilePath, item);

                if (document == null)
                {
                    _logger.LogWarning("Unable to copy {ItemUrl} because it cannot be found (AnnouncementID: {AnnoucementId}, MessageId: {MessageId}).", item.Url, announcement.Id, message.Id);
                    errors.Add($"Unable to copy {item.Url} because it cannot be found  (AnnouncementID: {announcement.Id}, MessageId: {message.Id}).");
                    continue;
                }

                // Check if document is new or not:
                if (document.Id == Guid.Empty)
                {
                    await _announcementDocumentsRepository.InsertAsync(new AnnouncementDocument
                    {
                        Announcement = announcement,
                        Document = document
                    });
                }
            }

            await _documentRepository.SaveChangesAsync();
        }

        /// <summary>
        /// Migrates business related files.
        /// </summary>
        /// <param name="submission">The parten submission.</param>
        /// <param name="business">The business object to migrate.</param>
        /// <param name="businessSchemaTypeName">The technical name of the business object to migrate.</param>
        /// <param name="errors">The current errors.</param>
        /// <returns>An awaitable taks.</returns>
        public async Task MigrateBusinessFiles(Submission submission, BusinessSchema business, string businessSchemaTypeName, List<string> errors)
        {
            ArgumentNullException.ThrowIfNull(submission, nameof(submission));
            ArgumentNullException.ThrowIfNull(errors, nameof(errors));

            if (business == null)
            {
                return;
            }

            EnsureBlobConnections();

            string targetFilePathPrefix = $"Submissions/{submission.Id}/{businessSchemaTypeName}";

            if (business.BusinessPlanFiles != null && business.BusinessPlanFiles.Count != 0)
            {
                // Business plans:
                foreach (var item in business.BusinessPlanFiles)
                {
                    string targetFilePath = targetFilePathPrefix + "/BusinessPlan/" + item.BlobName;

                    await CreateFormDocumentDocument(targetFilePath, submission, item, errors);
                }
            }

            if (business.DecisionMakingEvidenceFiles != null && business.DecisionMakingEvidenceFiles.Count != 0)
            {
                // Decision making evidence:
                foreach (var item in business.DecisionMakingEvidenceFiles)
                {
                    string targetFilePath = targetFilePathPrefix + "/DecisionMakingEvidence/" + item.BlobName;

                    await CreateFormDocumentDocument(targetFilePath, submission, item, errors);
                }
            }

            if (business.OtherEvidenceFiles != null && business.OtherEvidenceFiles.Count != 0)
            {
                // Other evidence:
                foreach (var item in business.OtherEvidenceFiles)
                {
                    string targetFilePath = targetFilePathPrefix + "/OtherEvidence/" + item.BlobName;

                    await CreateFormDocumentDocument(targetFilePath, submission, item, errors);
                }
            }

            await _documentRepository.SaveChangesAsync();
        }

        /// <summary>
        /// Migrates financial period related files.
        /// </summary>
        /// <param name="submission">The parten submission.</param>
        /// <param name="financialPeriodDetails">Financial period details.</param>
        /// <param name="errors">The current errors.</param>
        /// <returns>An awaitable taks.</returns>
        public async Task MigrateFinancialPeriodFiles(Submission submission, FinancialPeriodDetailsSchema financialPeriodDetails, List<string> errors)
        {
            ArgumentNullException.ThrowIfNull(submission, nameof(submission));
            ArgumentNullException.ThrowIfNull(errors, nameof(errors));

            if (financialPeriodDetails == null || financialPeriodDetails.Files == null || financialPeriodDetails.Files.Count == 0)
            {
                return;
            }

            EnsureBlobConnections();

            foreach (var item in financialPeriodDetails.Files)
            {
                string targetFilePath = $"Submissions/{submission.Id}/FinancialPeriod/{financialPeriodDetails.FinancialPeriodBegins.ToString("yyyyMMdd")}/{item.BlobName}";

                await CreateFormDocumentDocument(targetFilePath, submission, item, errors);
            }

            await _documentRepository.SaveChangesAsync();
        }

        /// <summary>
        /// Migrates tax residency related files.
        /// </summary>
        /// <param name="submission">The parten submission.</param>
        /// <param name="taxResidency">The tax redency info.</param>
        /// <param name="errors">The current errors.</param>
        /// <returns>An awaitable taks.</returns>
        public async Task MigrateTaxResidencyFiles(Submission submission, TaxResidencySchema taxResidency, List<string> errors)
        {
            ArgumentNullException.ThrowIfNull(submission, nameof(submission));
            ArgumentNullException.ThrowIfNull(errors, nameof(errors));

            if (taxResidency == null || taxResidency.EvidenceNonResidency == null || taxResidency.EvidenceNonResidency.Count == 0)
            {
                return;
            }

            EnsureBlobConnections();

            foreach (var item in taxResidency.EvidenceNonResidency)
            {
                string targetFilePath = $"Submissions/{submission.Id}/TaxResidency/EvidenceNonResidency/{item.BlobName}";

                await CreateFormDocumentDocument(targetFilePath, submission, item, errors);
            }

            await _documentRepository.SaveChangesAsync();
        }

        /// <summary>
        /// Migrates relevant activity related files.
        /// </summary>
        /// <param name="submission">The parten submission.</param>
        /// <param name="relevantActivities">The relevant activity info.</param>
        /// <param name="errors">The current errors.</param>
        /// <returns>An awaitable taks.</returns>
        public async Task MigrateRelevantActivitiesFiles(Submission submission, RelevantActivitiesSchema relevantActivities, List<string> errors)
        {
            ArgumentNullException.ThrowIfNull(submission, nameof(submission));
            ArgumentNullException.ThrowIfNull(errors, nameof(errors));

            if (relevantActivities == null || relevantActivities.EvidenceNoneActivities == null || relevantActivities.EvidenceNoneActivities.Count == 0)
            {
                return;
            }

            EnsureBlobConnections();

            foreach (var item in relevantActivities.EvidenceNoneActivities)
            {
                string targetFilePath = $"Submissions/{submission.Id}/RelevantActivities/EvidenceNoneActivities/{item.BlobName}";

                await CreateFormDocumentDocument(targetFilePath, submission, item, errors);
            }

            await _documentRepository.SaveChangesAsync();
        }

        /// <summary>
        /// Migrates supporting detail files.
        /// </summary>
        /// <param name="submission">The parten submission.</param>
        /// <param name="supportingDetails">The supporting details info.</param>
        /// <param name="errors">The current errors.</param>
        /// <returns>An awaitable taks.</returns>
        public async Task MigrateSupportingDetailsFiles(Submission submission, SupportingDetailsSchema supportingDetails, List<string> errors)
        {
            ArgumentNullException.ThrowIfNull(submission, nameof(submission));
            ArgumentNullException.ThrowIfNull(errors, nameof(errors));

            if (supportingDetails == null || supportingDetails.SupportAttachments == null || supportingDetails.SupportAttachments.Count == 0)
            {
                return;
            }

            EnsureBlobConnections();

            foreach (var item in supportingDetails.SupportAttachments)
            {
                string targetFilePath = $"Submissions/{submission.Id}/SupportingDetails/{item.BlobName}";

                await CreateFormDocumentDocument(targetFilePath, submission, item, errors);
            }

            await _documentRepository.SaveChangesAsync();
        }

        private void EnsureBlobConnections()
        {
            if (_sourceBlobStorage == null)
            {
                var sourceConfig = _documentOptions.Value.StorageAccountOptions.StorageAccounts.FirstOrDefault(x => x.Key == "SourceFileStorageBahamas");

                if (sourceConfig == null)
                {
                    throw new InvalidOperationException("Bahamas source document config not defined (Key: SourceFileStorageBahamas)!");
                }

                _sourceBlobStorage = GetBlobClient(sourceConfig);
            }

            if (_targetBlobStorage == null)
            {
                var targetConfig = _documentOptions.Value.StorageAccountOptions.StorageAccounts.FirstOrDefault(x => x.Key == "default");

                if (targetConfig == null)
                {
                    throw new InvalidOperationException("Bahamas target document config not defined (Key: default)!");
                }

                _targetBlobStorage = GetBlobClient(targetConfig);
            }
        }

        private BlobContainerClient GetBlobClient(StorageAccount storageAccountConfig)
        {
            var serviceUri = new Uri("https://" + storageAccountConfig.AccountName + ".blob.core.windows.net");

#if DEBUG
            if (storageAccountConfig.AccountName == "devstoreaccount1")
            {
                serviceUri = new Uri("http://127.0.0.1:10000/devstoreaccount1");
            }
#endif

            if (!string.IsNullOrEmpty(storageAccountConfig.SharedKey))
            {
                try
                {
                    StorageSharedKeyCredential credential = new StorageSharedKeyCredential(storageAccountConfig.AccountName, storageAccountConfig.SharedKey);
                    BlobServiceClient blobServiceClient = new BlobServiceClient(serviceUri, credential);
                    var blobContainerClient = blobServiceClient.GetBlobContainerClient(storageAccountConfig.DefaultContainer);
                    blobContainerClient.CreateIfNotExists();
                    return blobContainerClient;
                }
                catch (FormatException formatEx)
                {
                    _logger.LogWarning(formatEx, "Unable to connect to storage account '{AccountName}' because the shared access key is not valid", storageAccountConfig.AccountName);

                }
                catch (ArgumentNullException argNullEx)
                {
                    _logger.LogWarning(argNullEx, "Unable to connect to storage account '{AccountName}' because the shared access key is not valid", storageAccountConfig.AccountName);

                }
                catch (Azure.RequestFailedException ex)
                {
                    _logger.LogWarning(ex, "Failed to connect to storage account '{AccountName}' using the configured shared accesskey", storageAccountConfig.AccountName);
                }
            }

            try
            {
                DefaultAzureCredentialOptions options = new DefaultAzureCredentialOptions
                {
                    ExcludeEnvironmentCredential = true,
                    ExcludeManagedIdentityCredential = false,
                    ExcludeSharedTokenCacheCredential = true,
                    ExcludeVisualStudioCredential = true,
                    ExcludeVisualStudioCodeCredential = true,
                    ExcludeAzureCliCredential = true,
                    ExcludeInteractiveBrowserCredential = true
                };
                DefaultAzureCredential credential2 = new DefaultAzureCredential(options);
                BlobServiceClient blobServiceClient = new BlobServiceClient(serviceUri, credential2);
                var blobContainerClient = blobServiceClient.GetBlobContainerClient(storageAccountConfig.DefaultContainer);
                blobContainerClient.CreateIfNotExists();
                return blobContainerClient;
            }
            catch (ArgumentException argEx)
            {
                _logger.LogError(argEx, "Failed to connect to storage account '{AccountName}' no credential types allowed!", storageAccountConfig.AccountName);
            }
            catch (Azure.RequestFailedException ex)
            {
                _logger.LogError(ex, "Failed to connect to storage account '{AccountName}' using the credentials of managed identity", storageAccountConfig.AccountName);
            }

            return null;
        }

        private async Task CreateFormDocumentDocument(string targetFilePath, Submission submission, FileSchema item, List<string> errors)
        {
            // Create the document in the database:
            var document = await UploadAndRegisterDocument(targetFilePath, item);

            if (document == null)
            {
                _logger.LogWarning("Unable to copy {ItemUrl} because it cannot be found (SubmissionID: {SubmissionID}).", item.Url, submission.Id);
                errors.Add($"Unable to copy {item.Url} because it cannot be found  (SubmissionID: {submission.Id}).");
                return;
            }

            // Check if document is new or not:
            if (document.Id == Guid.Empty)
            {
                await _formDocumentDocumentsRepository.InsertAsync(new FormDocumentDocument
                {
                    Document = document,
                    FormDocument = submission.FormDocument,
                });
            }
        }

        private async Task<Framework.Services.Documents.EFModels.Document> UploadAndRegisterDocument(string targetFilePath, FileSchema item)
        {
            // Strip host and container name from the full URL:
            string sourceFilePath = string.Join("", new Uri(item.Url).Segments.Skip(2));

#if DEBUG
            sourceFilePath = "65f97eb224aaa05ddcb55bf6/rfi-file-d34eafad-737f-4c4a-a27e-53fa43ffe786.pdf";
#endif

            var sourceBlobClient = _sourceBlobStorage.GetBlobClient(sourceFilePath);

            if (!(await sourceBlobClient.ExistsAsync()))
            {
                return null;
            }

            // No need to customize the filename because this is already done by the source.
            var targetBlobClient = _targetBlobStorage.GetBlobClient(targetFilePath);

            // Check if target file exist, if not upload:
            if (!(await targetBlobClient.ExistsAsync()))
            {
                await targetBlobClient.UploadAsync(await sourceBlobClient.OpenReadAsync());

                var metaDataResult = targetBlobClient.SetMetadataAsync(new Dictionary<string, string>
                {
                    {"OriginalName", item.Originalname},
                    {"OriginalFileId", item.FileId},
                });
            }

            string blobPath = targetBlobClient.Uri.ToString();

            // Find possible existing document record:
            var document = await _documentRepository.FindFirstOrDefaultByConditionAsync(d => d.BlobPath == blobPath);

            if (document == null)
            {
                // Create the document in the database:
                document = await _documentRepository.InsertAsync(new Framework.Services.Documents.EFModels.Document
                {
                    BlobPath = blobPath,
                });
            }

            document.Container = targetBlobClient.BlobContainerName;
            document.BlobName = targetBlobClient.Name;
            document.Type = (int)GetDocumentType(item.BlobName);
            document.StorageAccount = targetBlobClient.AccountName;
            document.Description = item.Fieldname;
            document.Filename = item.Originalname;
            document.FileSize = long.TryParse(item.Size, out long fileSize) ? fileSize : 0;

            return document;
        }

        private static DocumentType GetDocumentType(string blobName)
        {
            if (blobName.EndsWith(".pdf", StringComparison.OrdinalIgnoreCase))
            {
                return DocumentType.Pdf;
            }
            else if (blobName.EndsWith(".xlsx", StringComparison.OrdinalIgnoreCase) || blobName.EndsWith(".xls", StringComparison.OrdinalIgnoreCase))
            {
                return DocumentType.Xls;
            }
            else if (blobName.EndsWith(".jpeg", StringComparison.OrdinalIgnoreCase) || blobName.EndsWith(".jpg", StringComparison.OrdinalIgnoreCase) || blobName.EndsWith(".png", StringComparison.OrdinalIgnoreCase) || blobName.EndsWith(".bmp", StringComparison.OrdinalIgnoreCase))
            {
                return DocumentType.Image;
            }

            return DocumentType.Unknown;
        }
    }
}
