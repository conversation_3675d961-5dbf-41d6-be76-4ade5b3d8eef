{"ApplicationInsights": {"EnableAdaptiveSampling": false}, "Serilog": {"Using": ["Serilog.Sinks.Console"], "MinimumLevel": {"Default": "Debug", "Override": {"Microsoft": "Information", "Microsoft.AspNetCore": "Warning", "System": "Information", "Microsoft.EntityFrameworkCore.Database.Command": "Warning", "Microsoft.Extensions.Diagnostics.HealthChecks": "Debug", "Hangfire": "Warning", "NetProGroup.Trust.DataMigration": "Verbose"}}, "WriteTo": [{"Name": "<PERSON><PERSON><PERSON>", "Args": {"outputTemplate": "[{Timestamp:HH:mm:ss} {Level:u3}] {Message:lj} [{SourceContext}] {NewLine}{Exception}", "restrictedToMinimumLevel": "Warning"}}], "Enrich": ["FromLogContext", "WithMachineName", "WithThreadId"]}, "AllowedHosts": "*", "DataMigration": {"ProgressUpdateInterval": 100, "UseNewBrandingLimitDate": "2023-11-08T00:00:00Z", "UseDummyInitialSync": false, "Enabled": true, "StoreUnprocessedRecords": true, "JobLockRefreshMarginSeconds": 120, "IgnorePaymentWhenNoInvoiceNumber": true, "ActiveJurisdiction": "<PERSON><PERSON><PERSON>", "JurisdictionSettings": {"Nevis": {"MongoConnectionString": "tbd", "MongoDatabaseName": "tbd"}, "Bahamas": {"MongoConnectionString": "tbd", "MongoDatabaseName": "tbd", "Document": {"StorageAccountOptions": {"StorageAccounts": [{"Key": "SourceFileStorageBahamas", "AccountName": "devstoreaccount1", "SharedKey": "Eby8vdM02xNOcqFlqUwJPLlmEtlCDXJ1OUzFT50uSRZ6IFsuFq2UVErCz4I6tq/K1SZFPTOtr/KBHBeksoGMGw==", "DefaultContainer": "tbvi-substance-uploads"}]}}}}}, "ShowPII": false, "ScheduledJobs": {"reports.nevis.contacts-info": {"CronExpression": "0 0 * * *", "Enabled": false}, "reports.nevis.companies-str-submission-status": {"CronExpression": "0 0 * * *", "Enabled": false}, "sync.viewpoint": {"CronExpression": "0 0 * * *", "Enabled": false}, "reports.panama.basic-financial-report": {"CronExpression": "0 0 * * *", "Enabled": false}, "submissions.scheduled": {"CronExpression": "0 0 * * *", "Enabled": false}, "reports.nevis.financial": {"CronExpression": "0 0 * * *", "Enabled": false}, "reports.nevis.submissions-not-paid": {"CronExpression": "0 0 * * *", "Enabled": false}}}