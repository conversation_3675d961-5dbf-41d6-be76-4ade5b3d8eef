// <copyright file="FinancialReportService.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

using Microsoft.Extensions.Logging;
using NetProGroup.Framework.Services.Documents;
using NetProGroup.Framework.Services.Locks;
using NetProGroup.Framework.Services.Locks.Models;
using NetProGroup.Trust.Application.Contracts.Common;
using NetProGroup.Trust.DataManager.Reports;
using NetProGroup.Trust.Domain.Report;
using NetProGroup.Trust.Domain.Report.Enum;
using NetProGroup.Trust.Domain.Repository;
using NetProGroup.Trust.Reports.Nevis.Financial;

namespace NetProGroup.Trust.Application.AppServices.Reports.Nevis.FinancialReports
{
    /// <summary>
    /// Financial report service implementation.
    /// </summary>
    public class FinancialReportService : IFinancialReportService
    {
        private readonly IFinancialReportGenerator _financialReportGenerator;
        private readonly IReportsDataManager _reportDataManager;
        private readonly IDocumentManager _documentManager;
        private readonly ILockManager _lockManager;
        private readonly ILogger _logger;
        private readonly IDateTimeProvider _dateTimeProvider;
        private readonly TrustDbContext _dbContext;

        /// <summary>
        /// Initializes a new instance of the <see cref="FinancialReportService"/> class.
        /// </summary>
        /// <param name="logger">The logger.</param>
        /// <param name="dateTimeProvider">The datetime provider.</param>
        /// <param name="financialReportGenerator">The financial report generator.</param>
        /// <param name="reportDataManager">The report data manager.</param>
        /// <param name="documentManager">The document manager.</param>
        /// <param name="lockManager">The lock manager.</param>
        /// <param name="dbContext">Instance of DbContext.</param>
        public FinancialReportService(
            ILogger<FinancialReportService> logger,
            IDateTimeProvider dateTimeProvider,
            IFinancialReportGenerator financialReportGenerator,
            IReportsDataManager reportDataManager,
            IDocumentManager documentManager,
            ILockManager lockManager,
            TrustDbContext dbContext)
        {
            _financialReportGenerator = financialReportGenerator;
            _reportDataManager = reportDataManager;
            _documentManager = documentManager;
            _lockManager = lockManager;
            _logger = logger;
            _dateTimeProvider = dateTimeProvider;
            _dbContext = dbContext;
        }

        /// <inheritdoc/>
        public async Task GenerateReportAsync(LockDTO jobLock)
        {
            ArgumentNullException.ThrowIfNull(jobLock);

            await _lockManager.RefreshLockAsync(jobLock.Id.Value);

            var fileTemplate = _financialReportGenerator.GenerateReportNameForTodayAsync();
            var fileName = $"{fileTemplate}.xlsx";

            if (await _reportDataManager.ReportExists(new Report
            {
                ReportName = fileTemplate,
                Type = ReportType.Financial
            }))
            {
                return;
            }

            var financialReportOutput = await _financialReportGenerator.GenerateTodayFinancialReportAsync();

            if (!financialReportOutput.Submissions.Any())
            {
                _logger.LogInformation("Financial Report couldn't find any data for the date: {Date}",
                    _dateTimeProvider.Now.ToString("yyyy-MM-dd"));
                return;
            }

            using var transaction = await _dbContext.Database.BeginTransactionAsync();

            try
            {
                var documentId = await _documentManager.CreateDocumentAsync(
                documentType: (int)ReportType.Financial,
                description: fileTemplate,
                fileName: fileName,
                data: financialReportOutput.FileContent,
                saveChanges: false);

                var report = await _reportDataManager.AddReport(new Report
                {
                    DocumentId = documentId,
                    ReportName = fileTemplate,
                    Type = ReportType.Financial,
                });

                await _financialReportGenerator.CompleteFinancialExport(financialReportOutput.Submissions, report.Id);

                await transaction.CommitAsync();
            }
            catch
            {
                await transaction.RollbackAsync();
                throw;
            }

            await _lockManager.RefreshLockAsync(jobLock.Id.Value);
        }
    }
}
