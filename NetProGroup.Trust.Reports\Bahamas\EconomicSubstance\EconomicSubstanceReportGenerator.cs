// <copyright file="BasicFinancialReportGenerator.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

using ClosedXML.Excel;
using NetProGroup.Trust.Application.Contracts.Common;
using NetProGroup.Trust.Application.Contracts.Submissions;
using NetProGroup.Trust.DataManager.Submissions;
using NetProGroup.Trust.DataManager.ReportTemplates;
using NetProGroup.Trust.Reports.ExcelTemplateExporter.Template;
using NetProGroup.Trust.Reports.Bahamas.EconomicSubstance.Populators;
using NetProGroup.Trust.Reports.Bahamas.EconomicSubstance;

namespace NetProGroup.Trust.Reports.Bahamas.EconomicSubstanceReport
{
    /// <summary>
    /// Generates reports for Bahamas's basic financial report module.
    /// </summary>
    /// <remarks>
    /// This report is an excel with all submissions not exported yet and with the attribute 'financial-period.tridentAccountingRecordsTool' set.
    /// The template is retrieved from the DataManager.
    /// </remarks>
    public class EconomicSubstanceReportGenerator : IEconomicSubstanceReportGenerator
    {
        private const string TemplateName = "economic-substance-report-submissions";
        private readonly IEconomicSubstanceReportRowPopulator _economicSubstanceReportRowPopulator;
        private readonly IDateTimeProvider _dateTimeProvider;
        private readonly IExcelTemplateService<ListSubmissionBahamasDTO> _excelTemplateService;
        private readonly ISubmissionReportsDataManager _submissionReportsDataManager;
        private readonly IReportTemplateProvider _templateProvider;

        /// <summary>
        /// Initializes a new instance of the <see cref="EconomicSubstanceReportGenerator"/> class.
        /// </summary>
        /// <param name="economicSubstanceReportRowPopulator">The submission line populator.</param>
        /// <param name="dateTimeProvider">The date time provider.</param>
        /// <param name="excelTemplateService">The excel template service.</param>
        /// <param name="submissionReportsDataManager">The submission reports datamanager.</param>
        /// <param name="templateProvider">The template provider.</param>
        public EconomicSubstanceReportGenerator(
            IEconomicSubstanceReportRowPopulator economicSubstanceReportRowPopulator,
            IDateTimeProvider dateTimeProvider,
            IExcelTemplateService<ListSubmissionBahamasDTO> excelTemplateService,
            ISubmissionReportsDataManager submissionReportsDataManager,
            IReportTemplateProvider templateProvider)
        {
            _excelTemplateService = excelTemplateService;
            _templateProvider = templateProvider;
            _economicSubstanceReportRowPopulator = economicSubstanceReportRowPopulator;
            _dateTimeProvider = dateTimeProvider;
            _submissionReportsDataManager = submissionReportsDataManager;
        }

        /// <inheritdoc/>
        public async Task<ReportOutput> GenerateSubmissionsReportAsync(List<ListSubmissionBahamasDTO> submissions)
        {
            var excelFileTemplate = await _templateProvider.GetExcelTemplateAsync(TemplateName);

            using var workbook = new XLWorkbook(excelFileTemplate);

            CreateSubmissionsReport(workbook, submissions);

            var stream = new MemoryStream();
            workbook.SaveAs(stream);

            return new ReportOutput(stream.ToArray());
        }

        /// <summary>
        /// Creates an Excel report for the given companies.
        /// </summary>
        /// <param name="workbook">The workbook to add the companies to.</param>
        /// <param name="submissions">The submissions to add in the report.</param>
        private void CreateSubmissionsReport(XLWorkbook workbook, List<ListSubmissionBahamasDTO> submissions)
        {
            var templateConfig = new TemplateConfiguration
            {
                TemplateRowCount = 1, // We have a single row per company
                HeaderRowCount = 1, // We have a single header row
                StartingRow = 3
            };
            _excelTemplateService.ClearRowPopulator();
            _excelTemplateService.RegisterRowPopulator(_economicSubstanceReportRowPopulator);
            _excelTemplateService.ApplyTemplate(workbook, submissions, templateConfig);
        }
    }
}