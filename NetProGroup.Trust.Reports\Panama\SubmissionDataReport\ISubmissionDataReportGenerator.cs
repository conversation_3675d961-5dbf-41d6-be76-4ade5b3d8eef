// <copyright file="ISubmissionDataReportGenerator.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

using NetProGroup.Framework.DependencyInjection.Interfaces;
using NetProGroup.Trust.Domain.Submissions;

namespace NetProGroup.Trust.Reports.Panama.SubmissionDataReport
{
    /// <summary>
    /// Interface for submission data report generator.
    /// </summary>
    public interface ISubmissionDataReportGenerator : ITransientService
    {
        /// <summary>
        /// Generates a submissions report for the module.
        /// </summary>
        /// <param name="submissions">The listo of submissions to be included in the report.</param>
        /// <returns>A <see cref="Task{ReportOutput}"/> representing the asynchronous operation.</returns>
        Task<ReportOutput> GenerateSubmissionDataReportAsync(List<Submission> submissions);
    }
}