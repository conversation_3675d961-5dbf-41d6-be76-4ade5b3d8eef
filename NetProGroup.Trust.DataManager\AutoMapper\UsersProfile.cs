﻿// <copyright file="UsersProfile.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

using AutoMapper;
using Microsoft.AspNetCore.Identity;
using NetProGroup.Framework.Services.Identity.EFModels;
using NetProGroup.Trust.Application.Contracts.Users;
using NetProGroup.Trust.DataManager.AutoMapper.DTOs;
using NetProGroup.Trust.Domain.Repository.Extensions;
using NetProGroup.Trust.Domain.Shared.Consts;
using NetProGroup.Trust.Domain.Shared.Enums;
using NetProGroup.Trust.Domain.Shared.Roles;
using System.Linq.Expressions;

namespace NetProGroup.Trust.DataManager.AutoMapper
{
    /// <summary>
    /// The users profile for AutoMapper.
    /// </summary>
    public class UsersProfile : Profile
    {
        /// <summary>
        /// Initializes a new instance of the <see cref="UsersProfile"/> class.
        /// </summary>
        public UsersProfile()
        {
            Expression<Func<List<IdentityUserRole<Guid>>, string>> primaryRoleLabelExpression =
                roles =>
                    roles.Count == 0 ? null
                    : roles.Any(role => role.RoleId == WellKnownRoleIds.Client) ?
                        "Client portal user" :
                        "Management portal user";

            var primaryRoleLabel = ExpressionExtensions.Combine<UserWithDetailsIntermediateDto, List<IdentityUserRole<Guid>>, string>(
                source => source.User.ApplicationUserRoles.ToList(),
                primaryRoleLabelExpression);

            Expression<Func<ApplicationUser, string>> emailExpression = src => src.Email == null ? string.Empty : src.Email.ToLower();

            // We never actually map directly from ApplicationUser to PCPApplicationUserDTO. This is used in the mapping from UserDTO to PCPApplicationUserDTO,
            // So that we don't have to map from src.User.Email, but can just say IncludeMembers(dto => dto.User) in the mapping from UserDto to PCPApplicationUserDTO,
            // And that will handle the mapping of the email property (and others).
            CreateMap<ApplicationUser, PCPApplicationUserDTO>()
               .ForMember(dest => dest.IsBlocked, opt => opt.MapFrom(src => src.LockoutEnabled))
               .ForMember(dest => dest.Email, opt => opt.MapFrom(emailExpression))
               .ForMember(dest => dest.PrimaryRoleLabel, opt => opt.Ignore())
               .ForMember(dest => dest.Permissions, opt => opt.Ignore())
               .ForMember(dest => dest.ApplicationUserRoles, opt => opt.Ignore())
               .ForMember(dest => dest.RoleIds, opt => opt.Ignore())
               .ForMember(dest => dest.RoleNames, opt => opt.Ignore())
               ;

            CreateMap<ApplicationUser, ListApplicationUsersDTO>()
                .ForMember(dest => dest.PrimaryRoleLabel, opt => opt.Ignore())
                .ForMember(dest => dest.RegistrationDate, opt => opt.Ignore())
                .ForMember(dest => dest.InitialSyncAt, opt => opt.Ignore())
                .ForMember(dest => dest.SyncStatus, opt => opt.Ignore())
                .ForMember(dest => dest.IsBlocked, opt => opt.MapFrom(src => src.LockoutEnabled))
                .ForMember(dest => dest.Email, opt => opt.MapFrom(emailExpression));

            CreateMap<UserWithDetailsIntermediateDto, ListApplicationUsersDTO>()
                .IncludeMembers(dto => dto.User)
                .ForMember(dest => dest.PrimaryRoleLabel, opt => opt.MapFrom(primaryRoleLabel))
                .ForMember(dest => dest.InitialSyncAt, opt => opt.MapFrom(src => src.Attributes.SingleOrDefault(a => a.Key == UserAttributeKeys.InitialSyncAt).Value))
                .ForMember(dest => dest.SyncStatus, opt => opt.MapFrom(src => src.MasterClientUsers.Any() ? SyncStatus.Active : SyncStatus.Inactive))
                .ForMember(dest => dest.RegistrationDate, opt => opt.MapFrom(src => src.Attributes.SingleOrDefault(a => a.Key == UserAttributeKeys.RegistrationAt).Value))
                ;
        }
    }
}
